<?php

/*
 * BEO Debug API Endpoint
 *
 * Access via: /api/beodebug
 *
 * Purpose: Trigger BEO merge tag generation with comprehensive debugging
 * for production troubleshooting of choice items not rendering in (*) categories
 *
 * Parameters:
 * - bentoUrl: Full Bento URL (will parse all linked objects automatically)
 * - contractId: Contract ID to test (alternative to bentoUrl)
 * - projectId: Project ID (optional, for additional context)
 * - menuId: Direct menu ID (alternative to contractId)
 * - categoryId: Optional - Specific category to test (defaults to duplicated category 20456830)
 * - testType: Optional - 'duplicated', 'original', or 'both' (default: 'both')
 * - email: Optional - Email address for debug logs (default: <EMAIL>)
 * - instance: Optional - Override instance (for testing different tenants)
 *
 * URL Examples:
 * /api/beodebug?bentoUrl=infinity#hq&1=hqt-projectTool-Projects&2=o-project-13322230-Test%20Event&3=pt-contractTools-Documents&4=o-contracts-20486180-Food%20BEO%E2%80%93COPY
 * /api/beodebug?contractId=20486180 (manual contract ID)
 * /api/beodebug?menuId=12345 (direct menu testing)
 * http://localhost:8080/api/_beodebug.php?bentoUrl=... (localhost)
 */

error_reporting(E_ALL);
ini_set('display_errors', '1');

date_default_timezone_set('America/Chicago');

if (extension_loaded('newrelic')) {
    newrelic_set_appname("Pagoda");
    newrelic_background_job();
    newrelic_ignore_apdex();
}

// Detect environment and set appropriate instance
$isLocalhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);

// Allow instance override via parameter for testing
$instanceOverride = $_GET['instance'] ?? $_POST['instance'] ?? null;

if ($instanceOverride) {
    // Manual instance override
    $instanceName = $instanceOverride;
    $_REQUEST['pagodaAPIKey'] = $instanceOverride;
} elseif ($isLocalhost) {
    // Localhost configuration
    $instanceName = 'rickyvoltz';
    $_REQUEST['pagodaAPIKey'] = 'rickyvoltz';
} else {
    // Production configuration
    $instanceName = 'infinity';
    $_REQUEST['pagodaAPIKey'] = 'infinity';
}

define("APP_ROOT", realpath(dirname(__FILE__)) . '/');

require APP_ROOT . 'DBPATH.php';
require_once APP_ROOT . '/lib/_.php';
require_once APP_ROOT . 'vendor/autoload.php';
require_once APP_ROOT . '_objects.php';
require_once APP_ROOT . '_communications.php';
require_once APP_ROOT . '_cookiesPG.php';
require_once APP_ROOT . 'files/_fileApi.php';
require_once APP_ROOT . '_excel.php';

// Database connection setup
$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");

// Initialize pgObjects with correct instance
if(!class_exists('pgObjects')){
    require_once '_pgObjectsMT.php';
}

// Initialize with minimal configuration to avoid undefined variable errors
$appConfig = array(
    'instance' => $instanceName,
    'db' => array(),
    'files' => array(),
    'twilio' => array()
);

// Create pgObjects with proper error handling
try {
    $pgObjects = new pgObjects($pdo, $instanceName);

    // Try to get instance configuration, but don't fail if it doesn't exist
    $instanceConfig = $pgObjects->where('instances', array('instance'=>$instanceName));

    if (!empty($instanceConfig) && is_array($instanceConfig[0])) {
        foreach($instanceConfig[0] as $k => $v){
            $appConfig[$k] = $v;
        }
    }
} catch (Exception $e) {
    // If instance lookup fails, continue with basic config
    error_log("Instance lookup failed: " . $e->getMessage());
}

// Initialize the app with proper multi-tenant setup
require_once '_app.php';

class BEODebugApp extends App {
    function __construct($appConfig, $dbConn, $pgObjects){
        $this->appConfig = $appConfig;
        $this->db = $dbConn;
        $this->pgObjects = $pgObjects;
    }
}

$app = new BEODebugApp($appConfig, $pdo, $pgObjects);

// Get parameters
$bentoUrl = $_GET['bentoUrl'] ?? $_POST['bentoUrl'] ?? null;
$contractId = $_GET['contractId'] ?? $_POST['contractId'] ?? null;
$projectId = $_GET['projectId'] ?? $_POST['projectId'] ?? null;
$menuId = $_GET['menuId'] ?? $_POST['menuId'] ?? null;
$categoryId = $_GET['categoryId'] ?? $_POST['categoryId'] ?? null;
$testType = $_GET['testType'] ?? $_POST['testType'] ?? 'both';
$email = $_GET['email'] ?? $_POST['email'] ?? '<EMAIL>';

// Parse Bento URL if provided
$parsedObjects = [];
if ($bentoUrl) {
    $parsedObjects = parseBentoUrl($bentoUrl);

    // Override individual parameters with parsed values
    if (!$contractId && !empty($parsedObjects['contractId'])) {
        $contractId = $parsedObjects['contractId'];
    }
    if (!$projectId && !empty($parsedObjects['projectId'])) {
        $projectId = $parsedObjects['projectId'];
    }
    if (!$menuId && !empty($parsedObjects['menuId'])) {
        $menuId = $parsedObjects['menuId'];
    }
}

/**
 * Parse Bento URL and extract all linked object IDs
 * URL format: infinity#hq&1=hqt-projectTool-Projects&2=o-project-13322230-Test%20Event&3=pt-contractTools-Documents&4=o-contracts-20486180-Food%20BEO%E2%80%93COPY
 */
function parseBentoUrl($url) {
    $parsed = [
        'instance' => null,
        'projectId' => null,
        'projectName' => null,
        'contractId' => null,
        'contractName' => null,
        'menuId' => null,
        'proposalId' => null,
        'rawSegments' => []
    ];

    // Remove full URL prefix if present, keep only the part after /app/
    if (strpos($url, '/app/') !== false) {
        $url = substr($url, strpos($url, '/app/') + 5);
    }

    // Extract instance from the beginning (before #)
    if (strpos($url, '#') !== false) {
        $parts = explode('#', $url, 2);
        $parsed['instance'] = $parts[0];
        $url = $parts[1];
    }

    // Split by & to get segments
    $segments = explode('&', $url);

    foreach ($segments as $segment) {
        if (empty($segment)) continue;

        // Parse segment format: 1=hqt-projectTool-Projects or 2=o-project-13322230-Test%20Event
        if (strpos($segment, '=') !== false) {
            list($index, $value) = explode('=', $segment, 2);
            $parsed['rawSegments'][$index] = $value;

            // Decode URL encoding
            $value = urldecode($value);

            // Extract project information
            if (preg_match('/o-project-(\d+)-(.+)/', $value, $matches)) {
                $parsed['projectId'] = $matches[1];
                $parsed['projectName'] = $matches[2];
            }

            // Extract contract information
            if (preg_match('/o-contracts-(\d+)-(.+)/', $value, $matches)) {
                $parsed['contractId'] = $matches[1];
                $parsed['contractName'] = $matches[2];
            }

            // Extract proposal information (if present)
            if (preg_match('/o-proposal-(\d+)-(.+)/', $value, $matches)) {
                $parsed['proposalId'] = $matches[1];
            }

            // Extract menu information (if present)
            if (preg_match('/o-menu-(\d+)-(.+)/', $value, $matches)) {
                $parsed['menuId'] = $matches[1];
            }
        }
    }

    return $parsed;
}

/**
 * Resolve all linked Bento objects and extract menu ID through proper relationships
 */
function resolveBentoObjects($pgObjects, $parsedObjects, $contractId, $projectId, &$debugLog) {
    $resolved = [
        'projectId' => null,
        'contractId' => null,
        'proposalId' => null,
        'menuId' => null,
        'project' => null,
        'contract' => null,
        'proposal' => null,
        'menu' => null
    ];

    // Use parsed objects as starting point
    $resolved['projectId'] = $parsedObjects['projectId'] ?? $projectId;
    $resolved['contractId'] = $parsedObjects['contractId'] ?? $contractId;

    // 1. Get Contract (if we have contract ID)
    if ($resolved['contractId']) {
        $debugLog[] = "Fetching contract: " . $resolved['contractId'];

        try {
            $contract = $pgObjects->getById('contracts', $resolved['contractId'], [
                'name' => true,
                'related_object' => true
            ], 2);

            if ($contract) {
                $resolved['contract'] = $contract;
                $debugLog[] = "Contract found: " . ($contract['name'] ?? 'UNNAMED');

                // Extract project from contract if not already set
                if (!$resolved['projectId'] && !empty($contract['related_object']['id'])) {
                    $resolved['projectId'] = $contract['related_object']['id'];
                    $debugLog[] = "Project ID extracted from contract: " . $resolved['projectId'];
                }

                // Try to extract menu directly from contract
                if (!empty($contract['related_object']['proposal']['menu'])) {
                    $resolved['menuId'] = $contract['related_object']['proposal']['menu'];
                    $debugLog[] = "Menu ID found in contract->related_object->proposal->menu: " . $resolved['menuId'];
                    return $resolved; // Found menu, we're done
                }
            }
        } catch (Exception $e) {
            $debugLog[] = "Error fetching contract: " . $e->getMessage();
        }
    }

    // 2. Get Project (if we have project ID)
    if ($resolved['projectId']) {
        $debugLog[] = "Fetching project: " . $resolved['projectId'];

        try {
            $project = $pgObjects->getById('projects', $resolved['projectId'], [
                'name' => true,
                'proposal' => true
            ], 2);

            if ($project) {
                $resolved['project'] = $project;
                $debugLog[] = "Project found: " . ($project['name'] ?? 'UNNAMED');

                // Extract proposal from project
                if (!empty($project['proposal']['id'])) {
                    $resolved['proposalId'] = $project['proposal']['id'];
                    $debugLog[] = "Proposal ID extracted from project: " . $resolved['proposalId'];

                    // Extract menu from proposal
                    if (!empty($project['proposal']['menu'])) {
                        $resolved['menuId'] = $project['proposal']['menu'];
                        $debugLog[] = "Menu ID found in project->proposal->menu: " . $resolved['menuId'];
                        return $resolved; // Found menu, we're done
                    }
                }
            }
        } catch (Exception $e) {
            $debugLog[] = "Error fetching project: " . $e->getMessage();
        }
    }

    // 3. Get Proposal directly (if we have proposal ID)
    if ($resolved['proposalId']) {
        $debugLog[] = "Fetching proposal: " . $resolved['proposalId'];

        try {
            $proposal = $pgObjects->getById('proposals', $resolved['proposalId'], [
                'name' => true,
                'menu' => true
            ], 1);

            if ($proposal) {
                $resolved['proposal'] = $proposal;
                $debugLog[] = "Proposal found: " . ($proposal['name'] ?? 'UNNAMED');

                // Extract menu from proposal
                if (!empty($proposal['menu'])) {
                    $resolved['menuId'] = $proposal['menu'];
                    $debugLog[] = "Menu ID found in proposal->menu: " . $resolved['menuId'];
                    return $resolved; // Found menu, we're done
                }
            }
        } catch (Exception $e) {
            $debugLog[] = "Error fetching proposal: " . $e->getMessage();
        }
    }

    $debugLog[] = "Menu resolution complete. Final menu ID: " . ($resolved['menuId'] ?? 'NOT FOUND');
    return $resolved;
}

// Default test categories from handoff document
$duplicatedCategoryId = 20456830; // "Food - 2025 Pricing (*)"
$originalCategoryId = 1162747;    // Original working category

// Response array
$response = [
    'success' => false,
    'message' => '',
    'data' => [],
    'debug_info' => []
];

// Validate required parameters
if (!$menuId && !$contractId && !$bentoUrl) {
    $response['message'] = 'Either bentoUrl, menuId, or contractId parameter is required';
    echo json_encode($response);
    exit;
}

// Log the test start
$debugLog = [];
$debugLog[] = "=== BEO DEBUG API ENDPOINT ===";
$debugLog[] = "Timestamp: " . date('Y-m-d H:i:s');
$debugLog[] = "Environment: " . ($isLocalhost ? 'LOCALHOST' : 'PRODUCTION');
$debugLog[] = "Host: " . $_SERVER['HTTP_HOST'];
$debugLog[] = "Instance: " . $instanceName;
$debugLog[] = "Instance Config: " . ($appConfig['instance'] ?? 'NOT SET');

// Log URL parsing results
if ($bentoUrl) {
    $debugLog[] = "\n=== BENTO URL PARSING ===";
    $debugLog[] = "Original URL: " . $bentoUrl;
    $debugLog[] = "Parsed Instance: " . ($parsedObjects['instance'] ?? 'NOT FOUND');
    $debugLog[] = "Parsed Project ID: " . ($parsedObjects['projectId'] ?? 'NOT FOUND');
    $debugLog[] = "Parsed Project Name: " . ($parsedObjects['projectName'] ?? 'NOT FOUND');
    $debugLog[] = "Parsed Contract ID: " . ($parsedObjects['contractId'] ?? 'NOT FOUND');
    $debugLog[] = "Parsed Contract Name: " . ($parsedObjects['contractName'] ?? 'NOT FOUND');
    $debugLog[] = "Parsed Proposal ID: " . ($parsedObjects['proposalId'] ?? 'NOT FOUND');
    $debugLog[] = "Parsed Menu ID: " . ($parsedObjects['menuId'] ?? 'NOT FOUND');
    $debugLog[] = "Raw Segments: " . json_encode($parsedObjects['rawSegments']);
}

$debugLog[] = "\n=== FINAL PARAMETERS ===";
$debugLog[] = "Contract ID: " . ($contractId ?? 'NOT PROVIDED');
$debugLog[] = "Project ID: " . ($projectId ?? 'NOT PROVIDED');
$debugLog[] = "Menu ID: " . ($menuId ?? 'TO BE EXTRACTED');
$debugLog[] = "Test Type: " . $testType;
$debugLog[] = "Email: " . $email;

try {
    // Resolve all linked objects and extract menu ID
    if (!$menuId) {
        $debugLog[] = "\n=== RESOLVING LINKED OBJECTS ===";

        $resolvedObjects = resolveBentoObjects($pgObjects, $parsedObjects, $contractId, $projectId, $debugLog);

        if (!empty($resolvedObjects['menuId'])) {
            $menuId = $resolvedObjects['menuId'];
            $debugLog[] = "Menu ID resolved: " . $menuId;
        } else {
            throw new Exception("Could not resolve menu ID from provided parameters");
        }
    }

    // Verify the menu exists
    $menu = $pgObjects->getById('', $menuId, 1, 1);
    if (!$menu) {
        throw new Exception("Menu with ID $menuId not found");
    }

    $debugLog[] = "\n=== MENU VERIFICATION ===";
    $debugLog[] = "Menu found: " . ($menu['name'] ?? 'UNNAMED');
    $debugLog[] = "Menu sections: " . count($menu['sections'] ?? []);

    // If we have project context, add it
    if ($projectId) {
        $project = $pgObjects->getById('projects', $projectId, ['name' => true]);
        if ($project) {
            $debugLog[] = "Project context: " . ($project['name'] ?? 'UNNAMED');
        }
    }

    // Test function
    function runBEOTest($app, $menuId, $categoryId, $categoryName, &$debugLog) {
        $debugLog[] = "\n=== TESTING CATEGORY: $categoryName (ID: $categoryId) ===";

        // Prepare POST data for the BEO function
        $_POST = (object)[
            'menuId' => $menuId,
            'options' => [$categoryId],
            'setup' => (object)[
                'border' => true,
                'sectionNames' => true,
                'internalNotes' => true,
                'tz' => 'America/Chicago',
                'choiceItems' => true
            ]
        ];

        // Capture output
        ob_start();
        $result = $app->generateInfinityBEOMergeTag();
        $output = ob_get_clean();

        $debugLog[] = "BEO generation completed";
        $debugLog[] = "HTML length: " . strlen($result['data'] ?? '');
        $debugLog[] = "Output captured: " . strlen($output) . " characters";

        return [
            'category_id' => $categoryId,
            'category_name' => $categoryName,
            'html_length' => strlen($result['data'] ?? ''),
            'html_content' => $result['data'] ?? '',
            'output' => $output,
            'success' => !empty($result['data'])
        ];
    }

    // Run tests based on testType
    $testResults = [];

    if ($testType === 'duplicated' || $testType === 'both') {
        $categoryToTest = $categoryId ?? $duplicatedCategoryId;
        $testResults['duplicated'] = runBEOTest(
            $app,
            $menuId,
            $categoryToTest,
            "Duplicated Category (*)",
            $debugLog
        );
    }

    if ($testType === 'original' || $testType === 'both') {
        $testResults['original'] = runBEOTest(
            $app,
            $menuId,
            $originalCategoryId,
            "Original Category",
            $debugLog
        );
    }

    // Additional data verification
    $debugLog[] = "\n=== DATA VERIFICATION ===";

    // Verify the test combination
    $testComboId = 20456859; // "Buffet Entree* (*)"
    $combo = $pgObjects->getById('inventory_billable_combinations', $testComboId);
    if ($combo) {
        $debugLog[] = "Test combination found: " . $combo['name'];
        $debugLog[] = "Combination category: " . $combo['category'];
        $debugLog[] = "Combination items count: " . count($combo['items'] ?? []);

        if (!empty($combo['items'])) {
            foreach ($combo['items'] as $i => $item) {
                $debugLog[] = "  Item $i: inventory_group = " . $item['inventory_group'];
            }
        }
    }

    // Verify referenced groups have choices
    $testGroupIds = [20434630, 20434495, 20434052];
    foreach ($testGroupIds as $groupId) {
        $group = $pgObjects->getById('inventory_billable_groups', $groupId);
        if ($group) {
            $choiceCount = count($group['items'][0]['choices'] ?? []);
            $debugLog[] = "Group {$group['name']}: $choiceCount choices";
        }
    }

    $response['success'] = true;
    $response['message'] = 'BEO debug test completed successfully';
    $response['data'] = $testResults;
    $response['debug_info'] = [
        'contract_id' => $contractId,
        'project_id' => $projectId,
        'menu_id' => $menuId,
        'menu_name' => $menu['name'] ?? 'UNNAMED',
        'menu_sections' => count($menu['sections'] ?? []),
        'test_type' => $testType,
        'categories_tested' => array_keys($testResults)
    ];

} catch (Exception $e) {
    $debugLog[] = "ERROR: " . $e->getMessage();
    $response['message'] = 'Error: ' . $e->getMessage();
}

// Email the debug log
$debugLog[] = "\n=== END DEBUG SESSION ===";
$debugContent = implode("\n", $debugLog);

$subject = "BEO Debug API Test - Menu $menuId - " . date('Y-m-d H:i:s');
$headers = "From: <EMAIL>\r\n";
$headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

mail($email, $subject, $debugContent, $headers);

// Add email info to response
$response['debug_info']['email_sent'] = $email;
$response['debug_info']['debug_log_lines'] = count($debugLog);

// Output JSON response
header('Content-Type: application/json');
echo json_encode($response, JSON_PRETTY_PRINT);

?>
