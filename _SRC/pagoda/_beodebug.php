<?php

/*
 * BEO Debug API Endpoint
 *
 * Access via: /api/beodebug
 *
 * Purpose: Trigger BEO merge tag generation with comprehensive debugging
 * for production troubleshooting of choice items not rendering in (*) categories
 *
 * Parameters:
 * - contractId: Contract ID to test (will extract menu from contract)
 * - projectId: Project ID (optional, for additional context)
 * - menuId: Direct menu ID (alternative to contractId)
 * - categoryId: Optional - Specific category to test (defaults to duplicated category 20456830)
 * - testType: Optional - 'duplicated', 'original', or 'both' (default: 'both')
 * - email: Optional - Email address for debug logs (default: <EMAIL>)
 * - instance: Optional - Override instance (for testing different tenants)
 *
 * URL Examples:
 * /api/beodebug?contractId=20486180 (auto-detects instance)
 * /api/beodebug?contractId=20486180&projectId=13322230
 * /api/beodebug?menuId=12345 (direct menu testing)
 * http://localhost:8080/api/_beodebug.php?contractId=123 (localhost)
 * /api/beodebug?contractId=123&instance=rickyvoltz (force instance)
 */

error_reporting(E_ALL);
ini_set('display_errors', '1');

date_default_timezone_set('America/Chicago');

if (extension_loaded('newrelic')) {
    newrelic_set_appname("Pagoda");
    newrelic_background_job();
    newrelic_ignore_apdex();
}

// Detect environment and set appropriate instance
$isLocalhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);

// Allow instance override via parameter for testing
$instanceOverride = $_GET['instance'] ?? $_POST['instance'] ?? null;

if ($instanceOverride) {
    // Manual instance override
    $instanceName = $instanceOverride;
    $_REQUEST['pagodaAPIKey'] = $instanceOverride;
} elseif ($isLocalhost) {
    // Localhost configuration
    $instanceName = 'rickyvoltz';
    $_REQUEST['pagodaAPIKey'] = 'rickyvoltz';
} else {
    // Production configuration
    $instanceName = 'infinity';
    $_REQUEST['pagodaAPIKey'] = 'infinity';
}

define("APP_ROOT", realpath(dirname(__FILE__)) . '/');

require APP_ROOT . 'DBPATH.php';
require_once APP_ROOT . '/lib/_.php';
require_once APP_ROOT . 'vendor/autoload.php';
require_once APP_ROOT . '_objects.php';
require_once APP_ROOT . '_communications.php';
require_once APP_ROOT . '_cookiesPG.php';
require_once APP_ROOT . 'files/_fileApi.php';
require_once APP_ROOT . '_excel.php';

// Database connection setup
$pdo = new PDO("pgsql:host=". $BENTO_DB_PATH .";port=". $BENTO_DB_PORT .";dbname=". $BENTO_DB_NAME .";sslmode=". $BENTO_DB_SSL .";user=". $BENTO_DB_USER .";password=". $BENTO_DB_PASSWORD ."");

// Initialize pgObjects with correct instance
if(!class_exists('pgObjects')){
    require_once '_pgObjectsMT.php';
}

// Initialize with minimal configuration to avoid undefined variable errors
$appConfig = array(
    'instance' => $instanceName,
    'db' => array(),
    'files' => array(),
    'twilio' => array()
);

// Create pgObjects with proper error handling
try {
    $pgObjects = new pgObjects($pdo, $instanceName);

    // Try to get instance configuration, but don't fail if it doesn't exist
    $instanceConfig = $pgObjects->where('instances', array('instance'=>$instanceName));

    if (!empty($instanceConfig) && is_array($instanceConfig[0])) {
        foreach($instanceConfig[0] as $k => $v){
            $appConfig[$k] = $v;
        }
    }
} catch (Exception $e) {
    // If instance lookup fails, continue with basic config
    error_log("Instance lookup failed: " . $e->getMessage());
}

// Initialize the app with proper multi-tenant setup
require_once '_app.php';

class BEODebugApp extends App {
    function __construct($appConfig, $dbConn, $pgObjects){
        $this->appConfig = $appConfig;
        $this->db = $dbConn;
        $this->pgObjects = $pgObjects;
    }
}

$app = new BEODebugApp($appConfig, $pdo, $pgObjects);

// Get parameters
$contractId = $_GET['contractId'] ?? $_POST['contractId'] ?? null;
$projectId = $_GET['projectId'] ?? $_POST['projectId'] ?? null;
$menuId = $_GET['menuId'] ?? $_POST['menuId'] ?? null;
$categoryId = $_GET['categoryId'] ?? $_POST['categoryId'] ?? null;
$testType = $_GET['testType'] ?? $_POST['testType'] ?? 'both';
$email = $_GET['email'] ?? $_POST['email'] ?? '<EMAIL>';

// Default test categories from handoff document
$duplicatedCategoryId = 20456830; // "Food - 2025 Pricing (*)"
$originalCategoryId = 1162747;    // Original working category

// Response array
$response = [
    'success' => false,
    'message' => '',
    'data' => [],
    'debug_info' => []
];

// Validate required parameters
if (!$menuId && !$contractId) {
    $response['message'] = 'Either menuId or contractId parameter is required';
    echo json_encode($response);
    exit;
}

// Log the test start
$debugLog = [];
$debugLog[] = "=== BEO DEBUG API ENDPOINT ===";
$debugLog[] = "Timestamp: " . date('Y-m-d H:i:s');
$debugLog[] = "Environment: " . ($isLocalhost ? 'LOCALHOST' : 'PRODUCTION');
$debugLog[] = "Host: " . $_SERVER['HTTP_HOST'];
$debugLog[] = "Instance: " . $instanceName;
$debugLog[] = "Instance Config: " . ($appConfig['instance'] ?? 'NOT SET');
$debugLog[] = "Contract ID: " . ($contractId ?? 'NOT PROVIDED');
$debugLog[] = "Project ID: " . ($projectId ?? 'NOT PROVIDED');
$debugLog[] = "Menu ID: " . ($menuId ?? 'TO BE EXTRACTED');
$debugLog[] = "Test Type: " . $testType;
$debugLog[] = "Email: " . $email;

try {
    // Extract menu ID from contract if needed
    if (!$menuId && $contractId) {
        $debugLog[] = "\n=== EXTRACTING MENU FROM CONTRACT ===";

        // Get the contract
        try {
            $contract = $pgObjects->getById('contracts', $contractId, [
                'name' => true,
                'related_object' => true
            ], 2);
        } catch (Exception $e) {
            throw new Exception("Error fetching contract $contractId: " . $e->getMessage());
        }

        if (!$contract) {
            throw new Exception("Contract with ID $contractId not found");
        }

        $debugLog[] = "Contract found: " . ($contract['name'] ?? 'UNNAMED');
        $debugLog[] = "Contract structure: " . json_encode($contract, JSON_PRETTY_PRINT);

        // Extract menu from contract's related object (project)
        $menuId = null;

        // Try different possible paths for menu extraction
        if (!empty($contract['related_object']['proposal']['menu'])) {
            $menuId = $contract['related_object']['proposal']['menu'];
            $debugLog[] = "Menu ID extracted from contract->related_object->proposal->menu: " . $menuId;
        } elseif (!empty($contract['related_object']['menu'])) {
            $menuId = $contract['related_object']['menu'];
            $debugLog[] = "Menu ID extracted from contract->related_object->menu: " . $menuId;
        } elseif (!empty($contract['menu'])) {
            $menuId = $contract['menu'];
            $debugLog[] = "Menu ID extracted from contract->menu: " . $menuId;
        }

        if (!$menuId) {
            throw new Exception("No menu found in contract $contractId. Available paths checked: related_object.proposal.menu, related_object.menu, menu");
        }
    }

    // Verify the menu exists
    $menu = $pgObjects->getById('', $menuId, 1, 1);
    if (!$menu) {
        throw new Exception("Menu with ID $menuId not found");
    }

    $debugLog[] = "\n=== MENU VERIFICATION ===";
    $debugLog[] = "Menu found: " . ($menu['name'] ?? 'UNNAMED');
    $debugLog[] = "Menu sections: " . count($menu['sections'] ?? []);

    // If we have project context, add it
    if ($projectId) {
        $project = $pgObjects->getById('projects', $projectId, ['name' => true]);
        if ($project) {
            $debugLog[] = "Project context: " . ($project['name'] ?? 'UNNAMED');
        }
    }

    // Test function
    function runBEOTest($app, $menuId, $categoryId, $categoryName, &$debugLog) {
        $debugLog[] = "\n=== TESTING CATEGORY: $categoryName (ID: $categoryId) ===";

        // Prepare POST data for the BEO function
        $_POST = (object)[
            'menuId' => $menuId,
            'options' => [$categoryId],
            'setup' => (object)[
                'border' => true,
                'sectionNames' => true,
                'internalNotes' => true,
                'tz' => 'America/Chicago',
                'choiceItems' => true
            ]
        ];

        // Capture output
        ob_start();
        $result = $app->generateInfinityBEOMergeTag();
        $output = ob_get_clean();

        $debugLog[] = "BEO generation completed";
        $debugLog[] = "HTML length: " . strlen($result['data'] ?? '');
        $debugLog[] = "Output captured: " . strlen($output) . " characters";

        return [
            'category_id' => $categoryId,
            'category_name' => $categoryName,
            'html_length' => strlen($result['data'] ?? ''),
            'html_content' => $result['data'] ?? '',
            'output' => $output,
            'success' => !empty($result['data'])
        ];
    }

    // Run tests based on testType
    $testResults = [];

    if ($testType === 'duplicated' || $testType === 'both') {
        $categoryToTest = $categoryId ?? $duplicatedCategoryId;
        $testResults['duplicated'] = runBEOTest(
            $app,
            $menuId,
            $categoryToTest,
            "Duplicated Category (*)",
            $debugLog
        );
    }

    if ($testType === 'original' || $testType === 'both') {
        $testResults['original'] = runBEOTest(
            $app,
            $menuId,
            $originalCategoryId,
            "Original Category",
            $debugLog
        );
    }

    // Additional data verification
    $debugLog[] = "\n=== DATA VERIFICATION ===";

    // Verify the test combination
    $testComboId = 20456859; // "Buffet Entree* (*)"
    $combo = $pgObjects->getById('inventory_billable_combinations', $testComboId);
    if ($combo) {
        $debugLog[] = "Test combination found: " . $combo['name'];
        $debugLog[] = "Combination category: " . $combo['category'];
        $debugLog[] = "Combination items count: " . count($combo['items'] ?? []);

        if (!empty($combo['items'])) {
            foreach ($combo['items'] as $i => $item) {
                $debugLog[] = "  Item $i: inventory_group = " . $item['inventory_group'];
            }
        }
    }

    // Verify referenced groups have choices
    $testGroupIds = [20434630, 20434495, 20434052];
    foreach ($testGroupIds as $groupId) {
        $group = $pgObjects->getById('inventory_billable_groups', $groupId);
        if ($group) {
            $choiceCount = count($group['items'][0]['choices'] ?? []);
            $debugLog[] = "Group {$group['name']}: $choiceCount choices";
        }
    }

    $response['success'] = true;
    $response['message'] = 'BEO debug test completed successfully';
    $response['data'] = $testResults;
    $response['debug_info'] = [
        'contract_id' => $contractId,
        'project_id' => $projectId,
        'menu_id' => $menuId,
        'menu_name' => $menu['name'] ?? 'UNNAMED',
        'menu_sections' => count($menu['sections'] ?? []),
        'test_type' => $testType,
        'categories_tested' => array_keys($testResults)
    ];

} catch (Exception $e) {
    $debugLog[] = "ERROR: " . $e->getMessage();
    $response['message'] = 'Error: ' . $e->getMessage();
}

// Email the debug log
$debugLog[] = "\n=== END DEBUG SESSION ===";
$debugContent = implode("\n", $debugLog);

$subject = "BEO Debug API Test - Menu $menuId - " . date('Y-m-d H:i:s');
$headers = "From: <EMAIL>\r\n";
$headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

mail($email, $subject, $debugContent, $headers);

// Add email info to response
$response['debug_info']['email_sent'] = $email;
$response['debug_info']['debug_log_lines'] = count($debugLog);

// Output JSON response
header('Content-Type: application/json');
echo json_encode($response, JSON_PRETTY_PRINT);

?>
