# BEO Production Debug Implementation

## Overview

I've implemented comprehensive production-safe debugging for the BEO merge tag issue. The debugging system will email detailed logs to `<EMAIL>` without interfering with normal application operation.

## What Was Added

### 1. Production Debug Logging in `generateInfinityBEOMergeTag()`

**File**: `_SRC/pagoda/_app.php` (lines ~5246-5850)

**Key Debug Points**:
- **Category Filtering**: Logs template categories, options passed, and final category list
- **Section Processing**: Logs each section name and item count
- **Item Category Matching**: For each item, logs:
  - Item name and category ID
  - Whether category matches filter list
  - Whether item is included or excluded
- **Choice Processing**: Logs:
  - Raw choice data
  - Available items count
  - Processed choices count
- **Item Structure**: Logs:
  - Items array count
  - Each sub-item name and ID
  - Whether sub-items have choices

### 2. Recipe Processing Debug in `get_full_recipe()`

**File**: `_SRC/pagoda/_app.php` (lines ~14108-14214)

**Key Debug Points**:
- **Recipe Processing**: Logs each item being processed
- **Inventory Group References**: Tracks all inventory_group IDs
- **Choice Resolution**: Logs choice processing at each level
- **Nested Structure**: Shows the complete nested inventory group hierarchy

### 3. Email Notification System

**Features**:
- Emails sent to `<EMAIL>`
- Subject includes menu ID and timestamp
- Plain text format for easy reading
- Automatic sending after each BEO generation
- Recipe debug logs sent periodically to avoid spam

## How to Use

### Step 1: Test Script Setup

1. Open the browser console on your production site
2. Copy and paste the contents of `_DOCS/BEO_Debug_Test_Script.js`
3. Replace `YOUR_MENU_ID_HERE` with an actual menu ID that contains items from both:
   - Duplicated category: `20456830` ("Food - 2025 Pricing (*)")
   - Original category: `1162747` (working)

### Step 2: Run Tests

```javascript
// In browser console:
runBEODebugTest();
```

This will:
1. Verify data integrity of the duplicated combination
2. Check that referenced groups have choices
3. Generate BEO merge tags for both categories
4. Email detailed debug logs

### Step 3: Analyze Results

Check your email for debug logs containing:

#### Category Filtering Analysis
- Which categories are being used for filtering
- Whether duplicated category `20456830` is included
- Item-by-item category matching results

#### Choice Processing Analysis
- Raw choice data structure
- How choices are being processed
- Whether choice items are being found and included

#### Recipe Structure Analysis
- Complete inventory group hierarchy
- Nested reference resolution
- Whether (*) groups are being properly processed

## Key Hypotheses to Test

Based on your handoff document, the debug logs will help test these hypotheses:

### 1. Category Filtering Issue
**Theory**: BEO merge tag filters by category and excludes (*) groups
**Debug Evidence**: Look for category mismatch messages in logs

### 2. Selection State Requirement
**Theory**: BEO requires specific selection state data that's missing
**Debug Evidence**: Compare choice processing between working and broken examples

### 3. Reference Resolution Issue
**Theory**: (*) groups aren't being properly resolved in nested structure
**Debug Evidence**: Check recipe processing logs for inventory_group resolution

### 4. Naming Pattern Issue
**Theory**: BEO has hardcoded logic that excludes (*) suffixed items
**Debug Evidence**: Look for items being excluded despite correct categories

## Expected Debug Output

You should receive emails with logs like:

```
=== BEO MERGE TAG DEBUG SESSION ===
Timestamp: 2025-07-11 14:30:00
Menu ID: 12345

=== CATEGORY FILTERING ===
Template categories: [1162747]
Options passed: [20456830]
Final categories for filtering: [1162747,20456830]

=== SECTION: Lunch ===
Items in section: 3

Item: Buffet Entree* (*)
  Category: 20456830
  Category in filter list: YES
  Filter list empty: NO
  RESULT: INCLUDED (category match)

  CHOICE PROCESSING for: Buffet Entree* (*)
    Raw choices: [{"choice":[123,456,789],"item":20456859}]
    Available items count: 5
    Processed choices count: 3

  ITEM STRUCTURE for: Buffet Entree* (*)
    Items array count: 5
      Item 0: Assorted Breads & Rolls (*) (ID: 20435097)
        Has choices: YES (2)
      Item 1: Salad Choice List (*) (ID: 20434630)
        Has choices: YES (19)
...
```

## Next Steps After Debug Results

1. **Analyze Email Logs**: Look for patterns in category filtering and choice processing
2. **Compare Working vs Broken**: Run tests on both categories and compare logs
3. **Identify Root Cause**: Use debug evidence to confirm which hypothesis is correct
4. **Implement Fix**: Based on findings, implement targeted fix
5. **Remove Debug Code**: Once issue is resolved, remove production debug code

## Safety Notes

- Debug code only logs and emails - no functional changes
- Original BEO functionality remains unchanged
- Debug emails sent only when `$productionDebug = true`
- Recipe debug logs throttled to prevent email spam
- All debug code can be easily removed by setting `$productionDebug = false`

## Files Modified

1. `_SRC/pagoda/_app.php` - Added debug logging to BEO functions
2. `_DOCS/BEO_Debug_Test_Script.js` - Browser console test script
3. `_DOCS/BEO_Production_Debug_Implementation.md` - This documentation

The implementation is ready for production testing. Run the test script and check your email for detailed debug information that should help identify why the (*) categories aren't showing choice items in BEO merge tags.
