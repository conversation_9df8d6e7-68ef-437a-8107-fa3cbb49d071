<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BEO Debug URL Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #005a87;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007cba;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .api-url {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .copy-btn {
            background-color: #28a745;
            font-size: 12px;
            padding: 5px 10px;
        }
        .copy-btn:hover {
            background-color: #218838;
        }
        .example {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .extracted-ids {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 BEO Debug URL Converter</h1>
        
        <div class="example">
            <h3>Example URL:</h3>
            <code>https://bento.infinityhospitality.net/app/infinity#hq&1=hqt-projectTool-Projects&2=o-project-13322230-Test%20Event&3=pt-contractTools-Documents&4=o-contracts-20486180-Food%20BEO%E2%80%93COPY</code>
        </div>

        <form id="converterForm">
            <div class="form-group">
                <label for="bentoUrl">Paste Your Bento URL Here:</label>
                <textarea id="bentoUrl" name="bentoUrl" 
                          placeholder="Paste the full URL from your browser address bar..."></textarea>
            </div>

            <button type="submit">🔍 Extract IDs & Generate API URL</button>
            <button type="button" onclick="clearResults()">🗑️ Clear</button>
        </form>

        <div id="results"></div>
    </div>

    <script>
        document.getElementById('converterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const url = document.getElementById('bentoUrl').value.trim();
            
            if (!url) {
                displayError('Please paste a URL');
                return;
            }
            
            try {
                const extracted = extractIdsFromUrl(url);
                displayResults(extracted);
            } catch (error) {
                displayError('Error parsing URL: ' + error.message);
            }
        });

        function extractIdsFromUrl(url) {
            const results = {
                projectId: null,
                contractId: null,
                projectName: null,
                contractName: null
            };
            
            // Extract project ID and name
            const projectMatch = url.match(/o-project-(\d+)-([^&]+)/);
            if (projectMatch) {
                results.projectId = projectMatch[1];
                results.projectName = decodeURIComponent(projectMatch[2]).replace(/%20/g, ' ');
            }
            
            // Extract contract ID and name
            const contractMatch = url.match(/o-contracts-(\d+)-([^&]+)/);
            if (contractMatch) {
                results.contractId = contractMatch[1];
                results.contractName = decodeURIComponent(contractMatch[2]).replace(/%20/g, ' ').replace(/%E2%80%93/g, '–');
            }
            
            if (!results.projectId && !results.contractId) {
                throw new Error('No project or contract IDs found in URL');
            }
            
            return results;
        }

        function displayResults(extracted) {
            const resultsDiv = document.getElementById('results');
            
            // Generate API URLs
            const baseApiUrl = '/api/beodebug';
            const apiUrls = [];
            
            if (extracted.contractId) {
                let url = `${baseApiUrl}?contractId=${extracted.contractId}`;
                if (extracted.projectId) {
                    url += `&projectId=${extracted.projectId}`;
                }
                apiUrls.push({
                    type: 'Contract-based (Recommended)',
                    url: url
                });
            }
            
            // Alternative URLs for different test types
            if (extracted.contractId) {
                apiUrls.push({
                    type: 'Test Duplicated Category Only',
                    url: `${baseApiUrl}?contractId=${extracted.contractId}&testType=duplicated`
                });
                
                apiUrls.push({
                    type: 'Test Original Category Only',
                    url: `${baseApiUrl}?contractId=${extracted.contractId}&testType=original`
                });
            }
            
            let html = `
                <div class="results success">
                    <h3>✅ IDs Extracted Successfully</h3>
                    
                    <div class="extracted-ids">
                        <h4>Extracted Information:</h4>
                        <ul>
                            ${extracted.projectId ? `<li><strong>Project ID:</strong> ${extracted.projectId}</li>` : ''}
                            ${extracted.projectName ? `<li><strong>Project Name:</strong> ${extracted.projectName}</li>` : ''}
                            ${extracted.contractId ? `<li><strong>Contract ID:</strong> ${extracted.contractId}</li>` : ''}
                            ${extracted.contractName ? `<li><strong>Contract Name:</strong> ${extracted.contractName}</li>` : ''}
                        </ul>
                    </div>
                    
                    <h4>Generated API URLs:</h4>
            `;
            
            apiUrls.forEach((apiUrl, index) => {
                html += `
                    <div style="margin-bottom: 15px;">
                        <strong>${apiUrl.type}:</strong>
                        <div class="api-url">${apiUrl.url}</div>
                        <button class="copy-btn" onclick="copyToClipboard('${apiUrl.url}')">📋 Copy URL</button>
                        <button class="copy-btn" onclick="openUrl('${apiUrl.url}')" style="background-color: #007cba;">🚀 Test Now</button>
                    </div>
                `;
            });
            
            html += `
                    <h4>Quick Test Links:</h4>
                    <p>Click "Test Now" buttons above, or copy URLs to test in:</p>
                    <ul>
                        <li>Browser (will show JSON response)</li>
                        <li>Postman/curl for API testing</li>
                        <li>Your application's debug interface</li>
                    </ul>
                    
                    <p><strong>📧 Debug logs will be <NAME_EMAIL></strong></p>
                </div>
            `;
            
            resultsDiv.innerHTML = html;
        }

        function displayError(message) {
            document.getElementById('results').innerHTML = `
                <div class="results error">
                    <h3>❌ Error</h3>
                    <p>${message}</p>
                    <p>Make sure your URL contains project or contract information like:</p>
                    <ul>
                        <li><code>o-project-12345-Project%20Name</code></li>
                        <li><code>o-contracts-67890-Contract%20Name</code></li>
                    </ul>
                </div>
            `;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('bentoUrl').value = '';
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Visual feedback
                event.target.textContent = '✅ Copied!';
                setTimeout(() => {
                    event.target.textContent = '📋 Copy URL';
                }, 2000);
            }).catch(function(err) {
                alert('Failed to copy: ' + err);
            });
        }

        function openUrl(url) {
            window.open(url, '_blank');
        }

        // Auto-fill example for testing
        document.addEventListener('DOMContentLoaded', function() {
            const exampleBtn = document.createElement('button');
            exampleBtn.textContent = '📝 Use Example URL';
            exampleBtn.type = 'button';
            exampleBtn.onclick = function() {
                document.getElementById('bentoUrl').value = 'https://bento.infinityhospitality.net/app/infinity#hq&1=hqt-projectTool-Projects&2=o-project-13322230-Test%20Event&3=pt-contractTools-Documents&4=o-contracts-20486180-Food%20BEO%E2%80%93COPY';
            };
            document.querySelector('.form-group').appendChild(exampleBtn);
        });
    </script>
</body>
</html>
