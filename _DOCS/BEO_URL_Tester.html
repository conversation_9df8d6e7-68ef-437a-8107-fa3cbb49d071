<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BEO Debug - URL Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 120px;
            resize: vertical;
            font-family: monospace;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #005a87;
        }
        .example {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007cba;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .parsed-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .api-url {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .copy-btn {
            background-color: #28a745;
            font-size: 12px;
            padding: 5px 10px;
        }
        .copy-btn:hover {
            background-color: #218838;
        }
        .test-btn {
            background-color: #dc3545;
            font-size: 12px;
            padding: 5px 10px;
        }
        .test-btn:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 BEO Debug - URL Tester</h1>
        
        <div class="example">
            <h3>Just Paste Your Bento URL!</h3>
            <p>Copy the entire URL from your browser and paste it below. The system will automatically extract all the linked objects (project, contract, menu, etc.) and test the BEO generation.</p>
            <p><strong>Example:</strong></p>
            <code>https://bento.infinityhospitality.net/app/infinity#hq&1=hqt-projectTool-Projects&2=o-project-13322230-Test%20Event&3=pt-contractTools-Documents&4=o-contracts-20486180-Food%20BEO%E2%80%93COPY</code>
        </div>

        <form id="urlTesterForm">
            <div class="form-group">
                <label for="bentoUrl">Paste Your Full Bento URL Here:</label>
                <textarea id="bentoUrl" name="bentoUrl" 
                          placeholder="Paste the complete URL from your browser address bar..."></textarea>
            </div>

            <div class="form-group">
                <label for="testType">Test Type</label>
                <select id="testType" name="testType">
                    <option value="both">Both Categories (Compare Working vs Broken)</option>
                    <option value="duplicated">Duplicated Category Only (*)</option>
                    <option value="original">Original Category Only</option>
                </select>
            </div>

            <div class="form-group">
                <label for="email">Email for Debug Logs</label>
                <input type="email" id="email" name="email" value="<EMAIL>" />
            </div>

            <button type="submit">🚀 Parse URL & Test BEO</button>
            <button type="button" onclick="clearResults()">🗑️ Clear</button>
            <button type="button" onclick="useExample()">📝 Use Example</button>
        </form>

        <div id="results"></div>
    </div>

    <script>
        document.getElementById('urlTesterForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const bentoUrl = document.getElementById('bentoUrl').value.trim();
            
            if (!bentoUrl) {
                displayError('Please paste a Bento URL');
                return;
            }
            
            // Parse the URL first to show what we extracted
            const parsed = parseUrl(bentoUrl);
            displayParsedInfo(parsed);
            
            // Generate API URL
            const apiUrl = generateApiUrl(bentoUrl);
            
            // Test the API
            try {
                const response = await fetch(apiUrl);
                const data = await response.json();
                displayApiResults(data, apiUrl);
            } catch (error) {
                displayError('API Test Error: ' + error.message);
            }
        });

        function parseUrl(url) {
            const parsed = {
                instance: null,
                projectId: null,
                projectName: null,
                contractId: null,
                contractName: null,
                menuId: null,
                proposalId: null
            };
            
            // Extract the part after /app/
            let urlPart = url;
            if (url.includes('/app/')) {
                urlPart = url.substring(url.indexOf('/app/') + 5);
            }
            
            // Extract instance
            if (urlPart.includes('#')) {
                const parts = urlPart.split('#');
                parsed.instance = parts[0];
                urlPart = parts[1];
            }
            
            // Parse segments
            const segments = urlPart.split('&');
            segments.forEach(segment => {
                if (!segment || !segment.includes('=')) return;
                
                const [index, value] = segment.split('=', 2);
                const decodedValue = decodeURIComponent(value);
                
                // Extract project
                const projectMatch = decodedValue.match(/o-project-(\d+)-(.+)/);
                if (projectMatch) {
                    parsed.projectId = projectMatch[1];
                    parsed.projectName = projectMatch[2];
                }
                
                // Extract contract
                const contractMatch = decodedValue.match(/o-contracts-(\d+)-(.+)/);
                if (contractMatch) {
                    parsed.contractId = contractMatch[1];
                    parsed.contractName = contractMatch[2];
                }
                
                // Extract proposal
                const proposalMatch = decodedValue.match(/o-proposal-(\d+)-(.+)/);
                if (proposalMatch) {
                    parsed.proposalId = proposalMatch[1];
                }
                
                // Extract menu
                const menuMatch = decodedValue.match(/o-menu-(\d+)-(.+)/);
                if (menuMatch) {
                    parsed.menuId = menuMatch[1];
                }
            });
            
            return parsed;
        }

        function generateApiUrl(bentoUrl) {
            const testType = document.getElementById('testType').value;
            const email = document.getElementById('email').value;
            
            // Extract just the part after /app/
            let urlPart = bentoUrl;
            if (bentoUrl.includes('/app/')) {
                urlPart = bentoUrl.substring(bentoUrl.indexOf('/app/') + 5);
            }
            
            const params = new URLSearchParams();
            params.append('bentoUrl', urlPart);
            params.append('testType', testType);
            if (email) params.append('email', email);
            
            // Determine base URL
            const isLocalhost = window.location.hostname === 'localhost';
            const baseUrl = isLocalhost 
                ? 'http://localhost:8080/api/_beodebug.php'
                : '/api/beodebug';
            
            return `${baseUrl}?${params.toString()}`;
        }

        function displayParsedInfo(parsed) {
            const resultsDiv = document.getElementById('results');
            
            let html = `
                <div class="parsed-info">
                    <h4>🔍 Parsed URL Information</h4>
                    <ul>
                        <li><strong>Instance:</strong> ${parsed.instance || 'Not found'}</li>
                        <li><strong>Project ID:</strong> ${parsed.projectId || 'Not found'}</li>
                        <li><strong>Project Name:</strong> ${parsed.projectName || 'Not found'}</li>
                        <li><strong>Contract ID:</strong> ${parsed.contractId || 'Not found'}</li>
                        <li><strong>Contract Name:</strong> ${parsed.contractName || 'Not found'}</li>
                        <li><strong>Proposal ID:</strong> ${parsed.proposalId || 'Not found'}</li>
                        <li><strong>Menu ID:</strong> ${parsed.menuId || 'Will be extracted from relationships'}</li>
                    </ul>
                </div>
            `;
            
            resultsDiv.innerHTML = html;
        }

        function displayApiResults(data, apiUrl) {
            const resultsDiv = document.getElementById('results');
            
            let html = resultsDiv.innerHTML; // Keep parsed info
            
            if (data.success) {
                html += `
                    <div class="results success">
                        <h3>✅ BEO Debug Test Completed</h3>
                        <p><strong>Message:</strong> ${data.message}</p>
                        
                        <h4>API URL Used:</h4>
                        <div class="api-url">${apiUrl}</div>
                        <button class="copy-btn" onclick="copyToClipboard('${apiUrl}')">📋 Copy URL</button>
                        
                        <h4>Results:</h4>
                        <ul>
                            <li><strong>Menu ID:</strong> ${data.debug_info.menu_id}</li>
                            <li><strong>Menu Name:</strong> ${data.debug_info.menu_name}</li>
                            <li><strong>Categories Tested:</strong> ${data.debug_info.categories_tested.join(', ')}</li>
                            <li><strong>Email Sent:</strong> ${data.debug_info.email_sent}</li>
                        </ul>
                        
                        <p><strong>📧 Check your email for detailed debug logs!</strong></p>
                    </div>
                `;
            } else {
                html += `
                    <div class="results error">
                        <h3>❌ BEO Debug Test Failed</h3>
                        <p><strong>Error:</strong> ${data.message}</p>
                        
                        <h4>API URL Used:</h4>
                        <div class="api-url">${apiUrl}</div>
                        <button class="copy-btn" onclick="copyToClipboard('${apiUrl}')">📋 Copy URL</button>
                        <button class="test-btn" onclick="window.open('${apiUrl}', '_blank')">🔍 Test in Browser</button>
                    </div>
                `;
            }
            
            resultsDiv.innerHTML = html;
        }

        function displayError(message) {
            document.getElementById('results').innerHTML = `
                <div class="results error">
                    <h3>❌ Error</h3>
                    <p>${message}</p>
                </div>
            `;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('bentoUrl').value = '';
        }

        function useExample() {
            document.getElementById('bentoUrl').value = 'https://bento.infinityhospitality.net/app/infinity#hq&1=hqt-projectTool-Projects&2=o-project-13322230-Test%20Event&3=pt-contractTools-Documents&4=o-contracts-20486180-Food%20BEO%E2%80%93COPY';
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                event.target.textContent = '✅ Copied!';
                setTimeout(() => {
                    event.target.textContent = '📋 Copy URL';
                }, 2000);
            }).catch(function(err) {
                alert('Failed to copy: ' + err);
            });
        }
    </script>
</body>
</html>
