# BEO Debug API - Localhost Testing

## Environment Detection

The API automatically detects the environment:

- **Localhost**: Uses `rickyvoltz` instance
- **Production**: Uses `infinity` instance

## Localhost Test URLs

### Basic Test
```
http://localhost:8080/api/_beodebug.php?contractId=YOUR_CONTRACT_ID
```

### With Project Context
```
http://localhost:8080/api/_beodebug.php?contractId=YOUR_CONTRACT_ID&projectId=YOUR_PROJECT_ID
```

### Test Different Categories
```
http://localhost:8080/api/_beodebug.php?contractId=YOUR_CONTRACT_ID&testType=both
http://localhost:8080/api/_beodebug.php?contractId=YOUR_CONTRACT_ID&testType=duplicated
http://localhost:8080/api/_beodebug.php?contractId=YOUR_CONTRACT_ID&testType=original
```

### Direct Menu Testing (if you have a menu ID)
```
http://localhost:8080/api/_beodebug.php?menuId=YOUR_MENU_ID
```

## Error Handling Improvements

The localhost version now handles:

1. **Missing Instance Configuration**: Won't crash if instance lookup fails
2. **Contract Structure Variations**: Tries multiple paths to find menu ID
3. **Database Connection Issues**: Better error reporting
4. **Missing Variables**: Initializes with safe defaults

## Debug Information

The debug logs will show:

```
=== BEO DEBUG API ENDPOINT ===
Timestamp: 2025-07-11 15:30:00
Environment: LOCALHOST
Host: localhost:8080
Instance: rickyvoltz
Instance Config: rickyvoltz
Contract ID: YOUR_CONTRACT_ID
...
```

## Common Localhost Issues & Solutions

### 1. "Contract not found"
**Cause**: Contract ID doesn't exist in localhost database
**Solution**: Use a contract ID that exists in your local rickyvoltz instance

### 2. "No menu found in contract"
**Cause**: Contract structure different in localhost
**Solution**: Check debug logs for contract structure, API tries multiple paths

### 3. Database connection errors
**Cause**: DBPATH.php environment variables not set correctly
**Solution**: Verify your local environment variables

### 4. Instance configuration missing
**Cause**: `instances` table doesn't have rickyvoltz entry
**Solution**: API continues with basic config, should still work

## Finding Test Data in Localhost

### Get Available Contracts
```sql
SELECT id, name FROM contracts WHERE id IS NOT NULL LIMIT 10;
```

### Get Contracts with Menus
```sql
SELECT c.id, c.name, c.related_object 
FROM contracts c 
WHERE c.related_object IS NOT NULL 
LIMIT 10;
```

### Get Available Menus
```sql
SELECT id, name FROM inventory_menu LIMIT 10;
```

## Example Localhost Workflow

1. **Find a contract with menu**:
   ```sql
   SELECT id, name FROM contracts WHERE related_object IS NOT NULL LIMIT 5;
   ```

2. **Test the API**:
   ```
   http://localhost:8080/api/_beodebug.php?contractId=FOUND_CONTRACT_ID
   ```

3. **Check response** for menu extraction success

4. **Review debug email** for detailed processing information

## Localhost vs Production Differences

| Aspect | Localhost | Production |
|--------|-----------|------------|
| Instance | `rickyvoltz` | `infinity` |
| Host Detection | `localhost:8080` | `bento.infinityhospitality.net` |
| Error Handling | More verbose | Production-safe |
| Debug Logs | Include structure dumps | Focused on BEO processing |

## Troubleshooting Steps

1. **Check if API loads**:
   ```
   http://localhost:8080/api/_beodebug.php
   ```
   Should return: `{"success":false,"message":"Either menuId or contractId parameter is required"...}`

2. **Test with menu ID directly**:
   ```
   http://localhost:8080/api/_beodebug.php?menuId=1
   ```

3. **Check debug email** for detailed error information

4. **Verify database connection** by checking other API endpoints

## Sample Test Commands

### curl Tests
```bash
# Basic test
curl "http://localhost:8080/api/_beodebug.php?contractId=123"

# With verbose output
curl -v "http://localhost:8080/api/_beodebug.php?contractId=123&testType=both"

# Direct menu test
curl "http://localhost:8080/api/_beodebug.php?menuId=456"
```

### Browser Tests
Just paste the URLs directly into your browser address bar to see the JSON response.

The API is now configured to work seamlessly in both localhost (`rickyvoltz`) and production (`infinity`) environments!
