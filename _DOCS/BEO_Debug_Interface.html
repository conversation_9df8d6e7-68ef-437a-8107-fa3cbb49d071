<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BEO Debug Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #005a87;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007cba;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007cba;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-result h4 {
            margin-top: 0;
            color: #007cba;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .info-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info-box h3 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 BEO Debug Interface</h1>

        <div class="info-box">
            <h3>About This Tool</h3>
            <p>This interface triggers the BEO merge tag generation with comprehensive debugging to help identify why choice items aren't rendering in duplicated (*) categories.</p>
            <p><strong>Debug logs will be emailed to the specified address.</strong></p>
            <p><strong>Easy Testing:</strong> Just copy contract and project IDs from your browser URL!</p>
        </div>

        <form id="debugForm">
            <div class="form-group">
                <label for="contractId">Contract ID (Recommended)</label>
                <input type="text" id="contractId" name="contractId"
                       placeholder="e.g., 20486180 (from URL: o-contracts-20486180-...)" />
                <small>Extract from URL like: o-contracts-<strong>20486180</strong>-Food%20BEO%E2%80%93COPY</small>
            </div>

            <div class="form-group">
                <label for="projectId">Project ID (Optional)</label>
                <input type="text" id="projectId" name="projectId"
                       placeholder="e.g., 13322230 (from URL: o-project-13322230-...)" />
                <small>Extract from URL like: o-project-<strong>13322230</strong>-Test%20Event</small>
            </div>

            <div class="form-group">
                <label for="menuId">OR Direct Menu ID</label>
                <input type="text" id="menuId" name="menuId"
                       placeholder="Enter menu ID directly (alternative to contract)" />
                <small>Use this if you want to test a menu directly without contract context</small>
            </div>

            <div class="form-group">
                <label for="testType">Test Type</label>
                <select id="testType" name="testType">
                    <option value="both">Both Categories (Recommended)</option>
                    <option value="duplicated">Duplicated Category Only (*)</option>
                    <option value="original">Original Category Only</option>
                </select>
                <small>Both categories allows comparison between working and broken</small>
            </div>

            <div class="form-group">
                <label for="categoryId">Custom Category ID (Optional)</label>
                <input type="text" id="categoryId" name="categoryId"
                       placeholder="Leave blank to use default (20456830)" />
                <small>Override the duplicated category ID if testing a different one</small>
            </div>

            <div class="form-group">
                <label for="email">Email for Debug Logs</label>
                <input type="email" id="email" name="email" value="<EMAIL>" />
                <small>Debug logs will be sent to this email address</small>
            </div>

            <button type="submit">🚀 Run BEO Debug Test</button>
            <button type="button" onclick="clearResults()">🗑️ Clear Results</button>
        </form>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Running BEO debug test... This may take a few moments.</p>
        </div>

        <div id="results"></div>
    </div>

    <script>
        document.getElementById('debugForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const params = new URLSearchParams();

            // Validate that we have either contractId or menuId
            const contractId = formData.get('contractId')?.trim();
            const menuId = formData.get('menuId')?.trim();

            if (!contractId && !menuId) {
                displayError('Please provide either a Contract ID or Menu ID');
                return;
            }

            for (let [key, value] of formData.entries()) {
                if (value.trim()) {
                    params.append(key, value);
                }
            }

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').innerHTML = '';

            try {
                // Construct API URL - you'll need to replace with your actual domain
                const apiUrl = `/api/beodebug?${params.toString()}`;

                const response = await fetch(apiUrl);
                const data = await response.json();

                displayResults(data);

            } catch (error) {
                displayError('Network error: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        });

        function displayResults(data) {
            const resultsDiv = document.getElementById('results');

            if (data.success) {
                resultsDiv.innerHTML = `
                    <div class="results success">
                        <h3>✅ Debug Test Completed Successfully</h3>
                        <p><strong>Message:</strong> ${data.message}</p>

                        <h4>Debug Information:</h4>
                        <ul>
                            ${data.debug_info.contract_id ? `<li><strong>Contract ID:</strong> ${data.debug_info.contract_id}</li>` : ''}
                            ${data.debug_info.project_id ? `<li><strong>Project ID:</strong> ${data.debug_info.project_id}</li>` : ''}
                            <li><strong>Menu ID:</strong> ${data.debug_info.menu_id}</li>
                            <li><strong>Menu:</strong> ${data.debug_info.menu_name}</li>
                            <li><strong>Sections:</strong> ${data.debug_info.menu_sections}</li>
                            <li><strong>Test Type:</strong> ${data.debug_info.test_type}</li>
                            <li><strong>Categories Tested:</strong> ${data.debug_info.categories_tested.join(', ')}</li>
                            <li><strong>Email Sent To:</strong> ${data.debug_info.email_sent}</li>
                            <li><strong>Debug Log Lines:</strong> ${data.debug_info.debug_log_lines}</li>
                        </ul>

                        ${generateTestResults(data.data)}

                        <p><strong>📧 Check your email for detailed debug logs!</strong></p>
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div class="results error">
                        <h3>❌ Debug Test Failed</h3>
                        <p><strong>Error:</strong> ${data.message}</p>
                    </div>
                `;
            }
        }

        function generateTestResults(testData) {
            if (!testData || Object.keys(testData).length === 0) {
                return '<p>No test results available.</p>';
            }

            let html = '<h4>Test Results:</h4>';

            for (let [testType, result] of Object.entries(testData)) {
                const statusIcon = result.success ? '✅' : '❌';
                const statusText = result.success ? 'Success' : 'Failed';

                html += `
                    <div class="test-result">
                        <h4>${statusIcon} ${result.category_name} - ${statusText}</h4>
                        <p><strong>Category ID:</strong> ${result.category_id}</p>
                        <p><strong>HTML Length:</strong> ${result.html_length} characters</p>
                        ${result.html_length > 0 ?
                            `<p><strong>Preview:</strong> HTML content generated successfully</p>` :
                            `<p><strong>Issue:</strong> No HTML content generated</p>`
                        }
                    </div>
                `;
            }

            return html;
        }

        function displayError(message) {
            document.getElementById('results').innerHTML = `
                <div class="results error">
                    <h3>❌ Error</h3>
                    <p>${message}</p>
                </div>
            `;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
