# BEO Debug API Test Examples

## Quick Test URLs

Based on your example URL structure, here are ready-to-use API endpoints:

### Using Your Example Contract (20486180)
```
https://bento.infinityhospitality.net/api/beodebug?contractId=20486180&projectId=13322230
```

### Test Different Scenarios

#### 1. Both Categories (Recommended)
```
https://bento.infinityhospitality.net/api/beodebug?contractId=20486180&testType=both
```

#### 2. Only Duplicated Category (*)
```
https://bento.infinityhospitality.net/api/beodebug?contractId=20486180&testType=duplicated
```

#### 3. Only Original Category
```
https://bento.infinityhospitality.net/api/beodebug?contractId=20486180&testType=original
```

#### 4. Custom Email
```
https://bento.infinityhospitality.net/api/beodebug?contractId=20486180&email=<EMAIL>
```

## How to Extract IDs from Your URLs

From this URL:
```
https://bento.infinityhospitality.net/app/infinity#hq&1=hqt-projectTool-Projects&2=o-project-13322230-Test%20Event&3=pt-contractTools-Documents&4=o-contracts-20486180-Food%20BEO%E2%80%93COPY
```

Extract:
- **Project ID**: `13322230` (from `o-project-13322230-...`)
- **Contract ID**: `20486180` (from `o-contracts-20486180-...`)

## Expected Response Format

### Success Response
```json
{
  "success": true,
  "message": "BEO debug test completed successfully",
  "data": {
    "duplicated": {
      "category_id": "20456830",
      "category_name": "Duplicated Category (*)",
      "html_length": 1234,
      "success": true
    },
    "original": {
      "category_id": "1162747", 
      "category_name": "Original Category",
      "html_length": 2345,
      "success": true
    }
  },
  "debug_info": {
    "contract_id": "20486180",
    "project_id": "13322230",
    "menu_id": "extracted_menu_id",
    "menu_name": "Menu Name",
    "menu_sections": 3,
    "test_type": "both",
    "categories_tested": ["duplicated", "original"],
    "email_sent": "<EMAIL>",
    "debug_log_lines": 45
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Contract with ID 20486180 not found",
  "data": [],
  "debug_info": []
}
```

## Multi-Tenant Configuration

The API endpoint is now properly configured for the **infinity** instance:

- ✅ **Instance**: `infinity` (not `voltzsoftware`)
- ✅ **Database**: Connects to infinity instance database
- ✅ **Configuration**: Uses infinity instance settings
- ✅ **Debug Logs**: Include instance information

## Testing Steps

### 1. Browser Test
1. Copy one of the URLs above
2. Paste into browser address bar
3. Press Enter
4. View JSON response
5. Check email for detailed debug logs

### 2. curl Test
```bash
curl "https://bento.infinityhospitality.net/api/beodebug?contractId=20486180&testType=both"
```

### 3. Postman Test
- Method: GET
- URL: `https://bento.infinityhospitality.net/api/beodebug`
- Params:
  - `contractId`: `20486180`
  - `testType`: `both`

## Debug Email Content

You'll receive an email with detailed logs like:

```
=== BEO DEBUG API ENDPOINT ===
Timestamp: 2025-07-11 15:30:00
Instance: infinity
Instance Config: infinity
Contract ID: 20486180
Project ID: 13322230
Menu ID: extracted_menu_id
Test Type: both
Email: <EMAIL>

=== EXTRACTING MENU FROM CONTRACT ===
Contract found: Food BEO–COPY
Menu ID extracted from contract: 12345

=== MENU VERIFICATION ===
Menu found: Test Menu
Menu sections: 3
Project context: Test Event

=== TESTING CATEGORY: Duplicated Category (*) (ID: 20456830) ===
BEO generation completed
HTML length: 1234
Output captured: 0 characters

=== TESTING CATEGORY: Original Category (ID: 1162747) ===
BEO generation completed
HTML length: 2345
Output captured: 0 characters

=== DATA VERIFICATION ===
Test combination found: Buffet Entree* (*)
Combination category: 20456830
Combination items count: 5
  Item 0: inventory_group = 20435097
  Item 1: inventory_group = 20434616
  Item 2: inventory_group = 20434630
  Item 3: inventory_group = 20434495
  Item 4: inventory_group = 20434052

Group Assorted Breads & Rolls (*): 2 choices
Group Salad Serving Style (*): 0 choices
Group Salad Choice List (*): 19 choices
Group Buffet Entree Choice List - 1 Selection (*): 30 choices
Group Sides Choice List - 3 Selections (*): 90 choices

=== END DEBUG SESSION ===
```

## Key Differences to Look For

When comparing the debug logs between duplicated and original categories:

1. **HTML Length**: Original should be longer if it has choice items
2. **Category Processing**: Check if items are being filtered correctly
3. **Choice Resolution**: Look for differences in choice processing
4. **Item Structure**: Compare nested inventory group structures

## Troubleshooting

### Common Issues

1. **"Contract not found"**: Verify contract ID is correct
2. **"No menu found in contract"**: Contract may not have associated menu
3. **"Menu not found"**: Extracted menu ID may be invalid
4. **Empty response**: Check instance configuration

### Debug Tips

1. Start with a simple test: `?contractId=20486180`
2. Check email logs for detailed processing information
3. Compare HTML lengths between categories
4. Look for category filtering issues in logs

The API is now properly configured for multi-tenant operation with the infinity instance!
